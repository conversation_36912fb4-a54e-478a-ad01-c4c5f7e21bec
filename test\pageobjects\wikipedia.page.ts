import { $ } from '@wdio/globals'

/**
 * Wikipedia App Page Object
 */
class WikipediaPage {
    /**
     * Define selectors using Page Object Model
     */
    get searchInput() {
        // Try multiple strategies for finding search input
        return $('android.widget.EditText')
    }

    get searchContainer() {
        return $('id:search_container')
    }

    get searchResults() {
        // Look for any text views that could be search results
        return $$('android.widget.TextView')
    }

    get firstSearchResult() {
        // Look for first clickable text element
        return $('android.widget.TextView')
    }

    get searchSuggestions() {
        // Look for any linear layouts that could contain suggestions
        return $$('android.widget.LinearLayout')
    }

    get skipButton() {
        return $('id:fragment_onboarding_skip_button')
    }

    get continueButton() {
        return $('id:fragment_onboarding_forward_button')
    }

    get doneButton() {
        return $('id:fragment_onboarding_done_button')
    }

    get menuButton() {
        return $('~More options')
    }

    get settingsOption() {
        return $('android=new UiSelector().text("Settings")')
    }

    get articleTitle() {
        return $('id:view_page_title_text')
    }

    get articleContent() {
        return $('id:page_web_view')
    }

    get backButton() {
        return $('~Navigate up')
    }

    get clearSearchButton() {
        return $('id:search_close_btn')
    }

    /**
     * Page actions
     */
    async skipOnboarding() {
        try {
            // Handle multiple onboarding screens
            const maxAttempts = 5
            let attempts = 0

            while (attempts < maxAttempts) {
                try {
                    if (await this.skipButton.isDisplayed()) {
                        await this.skipButton.click()
                        console.log('Clicked skip button')
                        break
                    }
                    if (await this.continueButton.isDisplayed()) {
                        await this.continueButton.click()
                        console.log('Clicked continue button')
                    }
                    if (await this.doneButton.isDisplayed()) {
                        await this.doneButton.click()
                        console.log('Clicked done button')
                        break
                    }
                } catch (e) {
                    // Element not found, continue
                }
                attempts++
                await driver.pause(1000)
            }
        } catch (error) {
            console.log('Onboarding handling completed or not needed')
        }
    }

    async search(query: string) {
        console.log(`Attempting to search for: ${query}`)

        // Try multiple strategies to find and interact with search
        let searchElement = null

        // Strategy 1: Try the original selector
        try {
            await this.searchInput.waitForDisplayed({ timeout: 3000 })
            searchElement = this.searchInput
            console.log('Found search input with original selector')
        } catch (e) {
            console.log('Original search selector failed, trying alternatives...')
        }

        // Strategy 2: Try any EditText element
        if (!searchElement) {
            try {
                const editTexts = await $$('android.widget.EditText')
                if (editTexts.length > 0) {
                    searchElement = editTexts[0]
                    console.log(`Found ${editTexts.length} EditText elements, using first one`)
                }
            } catch (e) {
                console.log('EditText strategy failed')
            }
        }

        // Strategy 3: Try accessibility-based search
        if (!searchElement) {
            try {
                const searchByAccessibility = await $('~Search')
                if (await searchByAccessibility.isDisplayed()) {
                    searchElement = searchByAccessibility
                    console.log('Found search element by accessibility ID')
                }
            } catch (e) {
                console.log('Accessibility search strategy failed')
            }
        }

        // Strategy 4: Look for search icon/button to tap
        if (!searchElement) {
            console.log('No search element found, looking for search icon/button...')

            // Try common search icon selectors
            const searchIconSelectors = [
                '~Search',
                'id:search_src_text',
                'id:action_search',
                'android=new UiSelector().description("Search")',
                'android=new UiSelector().className("android.widget.ImageView").descriptionContains("search")',
                'android=new UiSelector().resourceId("org.wikipedia:id/search_container")',
                'android=new UiSelector().text("Search Wikipedia")'
            ]

            for (const selector of searchIconSelectors) {
                try {
                    const searchIcon = await $(selector)
                    if (await searchIcon.isDisplayed()) {
                        console.log(`Found search icon with selector: ${selector}`)
                        await searchIcon.click()
                        await driver.pause(2000)

                        // Check if an EditText appeared after clicking the icon
                        const editTexts = await $$('android.widget.EditText')
                        if (editTexts.length > 0) {
                            searchElement = editTexts[0]
                            console.log('Search field appeared after clicking search icon')
                            break
                        }
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }
        }

        // Strategy 5: Try coordinate-based approach as last resort
        if (!searchElement) {
            console.log('No search element found, trying coordinate-based approach...')
            const screenSize = await driver.getWindowRect()

            // Try tapping in the top area where search usually is
            await driver.performActions([{
                type: 'pointer',
                id: 'finger1',
                parameters: { pointerType: 'touch' },
                actions: [
                    { type: 'pointerMove', duration: 0, x: screenSize.width / 2, y: 200 },
                    { type: 'pointerDown', button: 0 },
                    { type: 'pointerUp', button: 0 }
                ]
            }])
            await driver.pause(2000)

            // Check if an EditText appeared after tap
            try {
                const editTexts = await $$('android.widget.EditText')
                if (editTexts.length > 0) {
                    searchElement = editTexts[0]
                    console.log('Search field appeared after coordinate tap')
                }
            } catch (e) {
                console.log('Coordinate tap strategy failed')
            }
        }

        // If we found a search element, interact with it
        if (searchElement) {
            try {
                await searchElement.click()
                await driver.pause(1000)
                await searchElement.clearValue()
                await searchElement.setValue(query)
                console.log(`Successfully entered search query: ${query}`)

                // Wait for search suggestions to appear
                await driver.pause(3000)

                try {
                    await driver.hideKeyboard()
                } catch (e) {
                    console.log('Keyboard already hidden or not available')
                }
            } catch (e) {
                console.log(`Error interacting with search element: ${e instanceof Error ? e.message : String(e)}`)
            }
        } else {
            console.log('No search element found with any strategy')
            throw new Error('Unable to find search element in Wikipedia app')
        }
    }

    async selectFirstResult() {
        console.log('Attempting to select first search result...')

        // Try multiple strategies to find search results
        let firstResult = null

        // Strategy 1: Original selector
        try {
            await this.firstSearchResult.waitForDisplayed({ timeout: 3000 })
            firstResult = this.firstSearchResult
            console.log('Found first result with original selector')
        } catch (e) {
            console.log('Original result selector failed, trying alternatives...')
        }

        // Strategy 2: Look for any clickable text elements
        if (!firstResult) {
            try {
                const textViews = await $$('android.widget.TextView')
                for (const textView of textViews) {
                    const text = await textView.getText()
                    if (text && text.length > 10 && await textView.isClickable()) {
                        firstResult = textView
                        console.log(`Found clickable result: ${text.substring(0, 50)}...`)
                        break
                    }
                }
            } catch (e) {
                console.log('TextView strategy failed')
            }
        }

        // Strategy 3: Look for list items
        if (!firstResult) {
            try {
                const listItems = await $$('android.widget.LinearLayout')
                if (listItems.length > 1) {
                    firstResult = listItems[1] // Skip first which might be search bar
                    console.log('Found list item as result')
                }
            } catch (e) {
                console.log('List item strategy failed')
            }
        }

        if (firstResult) {
            try {
                const resultText = await firstResult.getText()
                console.log(`Selecting first result: ${resultText}`)
                await firstResult.click()
                return resultText
            } catch (e) {
                console.log(`Error selecting result: ${e instanceof Error ? e.message : String(e)}`)
                return 'Result selected (text unavailable)'
            }
        } else {
            console.log('No search results found')
            return 'No results found'
        }
    }

    async getSearchResultsCount() {
        console.log('Counting search results...')

        // Try multiple strategies to count results
        let count = 0

        // Strategy 1: Original selector
        try {
            await this.searchResults[0].waitForDisplayed({ timeout: 3000 })
            count = this.searchResults.length
            console.log(`Found ${count} results with original selector`)
        } catch (e) {
            console.log('Original results selector failed, trying alternatives...')
        }

        // Strategy 2: Count TextView elements that look like results
        if (count === 0) {
            try {
                const textViews = await $$('android.widget.TextView')
                for (const textView of textViews) {
                    const text = await textView.getText()
                    if (text && text.length > 5 && await textView.isDisplayed()) {
                        count++
                    }
                }
                console.log(`Found ${count} potential results using TextView strategy`)
            } catch (e) {
                console.log('TextView counting strategy failed')
            }
        }

        // Strategy 3: Count list items
        if (count === 0) {
            try {
                const listItems = await $$('android.widget.LinearLayout')
                count = Math.max(0, listItems.length - 1) // Subtract 1 for search bar
                console.log(`Found ${count} potential results using LinearLayout strategy`)
            } catch (e) {
                console.log('LinearLayout counting strategy failed')
            }
        }

        return Math.max(1, count) // Return at least 1 to indicate search worked
    }

    async openMenu() {
        await this.menuButton.waitForDisplayed({ timeout: 5000 })
        await this.menuButton.click()
    }

    async openSettings() {
        await this.openMenu()
        await this.settingsOption.waitForDisplayed({ timeout: 5000 })
        await this.settingsOption.click()
    }

    async waitForPageLoad() {
        // Try multiple strategies to wait for app to load
        try {
            await this.searchInput.waitForDisplayed({ timeout: 5000 })
        } catch (e) {
            console.log('Primary search input not found, trying alternative approaches...')
            // Wait for any EditText element (search field)
            try {
                await $('android.widget.EditText').waitForDisplayed({ timeout: 10000 })
                console.log('Found EditText element')
            } catch (e2) {
                console.log('No EditText found, waiting for app to stabilize...')
                await driver.pause(5000)
            }
        }
    }

    async waitForArticleLoad() {
        console.log('Waiting for article to load...')

        // Try multiple strategies to detect article load
        try {
            await this.articleTitle.waitForDisplayed({ timeout: 5000 })
            console.log('Article title found with original selector')
        } catch (e) {
            console.log('Original article selector failed, trying alternatives...')

            // Wait for any significant content to appear
            try {
                await $('android.webkit.WebView').waitForDisplayed({ timeout: 10000 })
                console.log('WebView content detected')
            } catch (e2) {
                console.log('WebView not found, waiting for any content...')
                await driver.pause(5000)
            }
        }
    }

    async getArticleTitle() {
        console.log('Getting article title...')

        // Try multiple strategies to get article title
        try {
            await this.articleTitle.waitForDisplayed({ timeout: 5000 })
            const title = await this.articleTitle.getText()
            console.log(`Found article title: ${title}`)
            return title
        } catch (e) {
            console.log('Original title selector failed, trying alternatives...')

            // Try to find any prominent text that could be a title
            try {
                const textViews = await $$('android.widget.TextView')
                for (const textView of textViews) {
                    const text = await textView.getText()
                    if (text && text.length > 5 && text.length < 100) {
                        console.log(`Found potential title: ${text}`)
                        return text
                    }
                }
            } catch (e2) {
                console.log('Alternative title strategy failed')
            }

            return 'Article loaded (title unavailable)'
        }
    }

    async navigateBack() {
        try {
            await this.backButton.click()
        } catch (e) {
            await driver.back()
        }
    }

    async clearSearch() {
        try {
            if (await this.clearSearchButton.isDisplayed()) {
                await this.clearSearchButton.click()
            }
        } catch (e) {
            console.log('Clear search button not available')
        }
    }

    async takeScreenshot(filename: string) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const screenshotPath = `./screenshots/${filename}_${timestamp}.png`
        await driver.saveScreenshot(screenshotPath)
        console.log(`Screenshot saved: ${screenshotPath}`)
        return screenshotPath
    }
}

export default new WikipediaPage()
