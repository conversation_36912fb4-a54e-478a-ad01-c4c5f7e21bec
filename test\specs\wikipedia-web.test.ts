import { expect } from '@wdio/globals'

describe('Wikipedia Mobile Web Testing', () => {
    beforeEach(async () => {
        console.log('=== Starting new web test ===')
        
        // Navigate to Wikipedia mobile site
        await driver.url('https://en.m.wikipedia.org')
        
        // Wait for page to load
        await driver.pause(3000)
        
        // Take screenshot at start
        await driver.saveScreenshot('./screenshots/web_test_start.png')
        
        console.log('Wikipedia mobile site is ready for testing')
    })

    /**
     * Test basic page load and element discovery
     */
    it('should load Wikipedia mobile site and discover elements', async () => {
        console.log('Step 1: Verifying page loaded')
        
        // Check if we're on the right page
        const title = await driver.getTitle()
        console.log(`Page title: ${title}`)
        expect(title).toContain('Wikipedia')
        
        // Take screenshot after page load
        await driver.saveScreenshot('./screenshots/web_page_loaded.png')
        
        console.log('Step 2: Discovering page elements')
        
        // Look for search elements
        try {
            const searchInput = await $('input[type="search"]')
            if (await searchInput.isDisplayed()) {
                console.log('✅ Found search input field')
                
                // Test search functionality
                await searchInput.setValue('Mobile testing')
                await driver.pause(1000)
                
                // Take screenshot after entering search
                await driver.saveScreenshot('./screenshots/web_search_entered.png')
                
                // Look for search button or submit
                const searchButton = await $('button[type="submit"]')
                if (await searchButton.isDisplayed()) {
                    await searchButton.click()
                    console.log('✅ Search submitted successfully')
                    
                    // Wait for results
                    await driver.pause(3000)
                    
                    // Take screenshot of results
                    await driver.saveScreenshot('./screenshots/web_search_results.png')
                } else {
                    console.log('⚠️ Search button not found, trying Enter key')
                    await searchInput.keys('Enter')
                    await driver.pause(3000)
                    await driver.saveScreenshot('./screenshots/web_search_results_enter.png')
                }
            } else {
                console.log('❌ Search input not visible')
            }
        } catch (error) {
            console.log(`❌ Error with search: ${error instanceof Error ? error.message : String(error)}`)
        }
        
        console.log('✅ Web element discovery test completed successfully')
    })

    /**
     * Test scrolling and navigation
     */
    it('should test scrolling and navigation', async () => {
        console.log('Step 1: Testing page scroll')
        
        // Take screenshot before scroll
        await driver.saveScreenshot('./screenshots/web_before_scroll.png')
        
        // Scroll down
        await driver.execute('window.scrollBy(0, 500)')
        await driver.pause(1000)
        
        // Take screenshot after scroll
        await driver.saveScreenshot('./screenshots/web_after_scroll.png')
        
        console.log('Step 2: Looking for navigation elements')
        
        // Look for common navigation elements
        const navElements = [
            'a[href*="Main_Page"]',
            'a[href*="Random"]',
            'button',
            'nav',
            '.menu'
        ]
        
        for (const selector of navElements) {
            try {
                const elements = await $$(selector)
                if (elements.length > 0) {
                    console.log(`✅ Found ${elements.length} elements with selector: ${selector}`)
                    
                    // Try to interact with the first element if it's clickable
                    const firstElement = elements[0]
                    if (await firstElement.isClickable()) {
                        const text = await firstElement.getText()
                        console.log(`  - Clickable element text: "${text}"`)
                    }
                } else {
                    console.log(`⚠️ No elements found with selector: ${selector}`)
                }
            } catch (error) {
                console.log(`❌ Error with selector ${selector}: ${error instanceof Error ? error.message : String(error)}`)
            }
        }
        
        console.log('✅ Scrolling and navigation test completed successfully')
    })

    afterEach(async () => {
        console.log('=== Cleaning up after web test ===')
        
        // Take final screenshot
        await driver.saveScreenshot('./screenshots/web_test_end.png')
        
        console.log('Web test cleanup completed')
    })
})
